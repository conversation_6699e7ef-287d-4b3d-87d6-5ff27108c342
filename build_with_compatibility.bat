@echo off
echo === Windows 7兼容性打包脚本 ===
echo.

REM 清理之前的构建文件
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del "*.spec"

echo 开始打包...
echo.

REM 使用兼容性参数打包
pyinstaller ^
    --onefile ^
    --windowed ^
    --name "照片处理器_兼容版" ^
    --add-data "*.py;." ^
    --hidden-import "PIL._tkinter_finder" ^
    --hidden-import "tkinter" ^
    --hidden-import "tkinter.ttk" ^
    --hidden-import "tkinter.filedialog" ^
    --hidden-import "tkinter.messagebox" ^
    --exclude-module "numpy" ^
    --exclude-module "matplotlib" ^
    --exclude-module "scipy" ^
    --noupx ^
    --clean ^
    photo_resizer.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ 打包成功！
    echo 可执行文件位置: dist\照片处理器_兼容版.exe
    echo.
    echo 请在Windows 7系统上测试此文件
) else (
    echo.
    echo ✗ 打包失败，请检查错误信息
)

pause
