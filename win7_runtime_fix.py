"""
Windows 7运行时修复工具
此脚本帮助解决在Windows 7上运行PyInstaller打包程序时的DLL缺失问题
"""

import os
import sys
import urllib.request
import subprocess
from pathlib import Path

def download_file(url, filename):
    """下载文件"""
    try:
        print(f"正在下载 {filename}...")
        urllib.request.urlretrieve(url, filename)
        print(f"✓ {filename} 下载完成")
        return True
    except Exception as e:
        print(f"✗ 下载失败: {e}")
        return False

def create_installer_script():
    """创建Windows 7运行时安装脚本"""
    script_content = '''@echo off
echo === Windows 7 运行时环境修复工具 ===
echo.
echo 此工具将安装必要的运行时库以解决DLL缺失问题
echo.

REM 检查是否为管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ 检测到管理员权限
) else (
    echo ⚠ 需要管理员权限来安装运行时库
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 正在安装 Microsoft Visual C++ Redistributable...
echo.

REM 安装 VC++ 2015-2022 Redistributable (x64)
if exist "vc_redist.x64.exe" (
    echo 安装 Visual C++ Redistributable x64...
    vc_redist.x64.exe /quiet /norestart
    if %ERRORLEVEL% EQU 0 (
        echo ✓ x64版本安装成功
    ) else (
        echo ⚠ x64版本安装可能失败，错误代码: %ERRORLEVEL%
    )
) else (
    echo ⚠ 未找到 vc_redist.x64.exe
)

REM 安装 VC++ 2015-2022 Redistributable (x86)
if exist "vc_redist.x86.exe" (
    echo 安装 Visual C++ Redistributable x86...
    vc_redist.x86.exe /quiet /norestart
    if %ERRORLEVEL% EQU 0 (
        echo ✓ x86版本安装成功
    ) else (
        echo ⚠ x86版本安装可能失败，错误代码: %ERRORLEVEL%
    )
) else (
    echo ⚠ 未找到 vc_redist.x86.exe
)

echo.
echo === 安装完成 ===
echo.
echo 请重新启动计算机，然后尝试运行您的程序
echo.
pause
'''
    
    with open('install_runtime_win7.bat', 'w', encoding='gbk') as f:
        f.write(script_content)
    
    print("✓ 创建了 install_runtime_win7.bat")

def create_download_script():
    """创建下载运行时库的脚本"""
    download_script = '''@echo off
echo === 下载Windows 7运行时库 ===
echo.

echo 正在下载 Microsoft Visual C++ Redistributable...
echo.

REM 下载链接（Microsoft官方）
set VC_X64_URL=https://aka.ms/vs/17/release/vc_redist.x64.exe
set VC_X86_URL=https://aka.ms/vs/17/release/vc_redist.x86.exe

echo 下载 x64 版本...
powershell -Command "Invoke-WebRequest -Uri '%VC_X64_URL%' -OutFile 'vc_redist.x64.exe'"

echo 下载 x86 版本...
powershell -Command "Invoke-WebRequest -Uri '%VC_X86_URL%' -OutFile 'vc_redist.x86.exe'"

echo.
echo ✓ 下载完成
echo 现在可以运行 install_runtime_win7.bat 来安装运行时库
echo.
pause
'''
    
    with open('download_runtime.bat', 'w', encoding='gbk') as f:
        f.write(download_script)
    
    print("✓ 创建了 download_runtime.bat")

def create_manual_fix_guide():
    """创建手动修复指南"""
    guide_content = '''# Windows 7 DLL缺失问题解决指南

## 问题描述
在Windows 7上运行PyInstaller打包的程序时出现：
- api-ms-win-core-path-1-1-0.dll 缺失
- 或其他类似的api-ms-win-*.dll缺失

## 解决方案

### 方案1：安装Microsoft Visual C++ Redistributable
1. 下载并安装最新的 Microsoft Visual C++ Redistributable
   - x64版本：https://aka.ms/vs/17/release/vc_redist.x64.exe
   - x86版本：https://aka.ms/vs/17/release/vc_redist.x86.exe
2. 重启计算机
3. 重新运行程序

### 方案2：使用兼容的Python版本重新打包
1. 安装Python 3.8或更早版本
2. 使用提供的 build_win7_compatible.py 脚本重新打包
3. 在Windows 7系统上测试

### 方案3：手动复制DLL文件
1. 从Windows 10系统的 C:\\Windows\\System32\\ 目录复制缺失的DLL文件
2. 将DLL文件放在exe程序同一目录下
3. 运行程序

### 方案4：使用虚拟环境
1. 在Windows 7系统上直接安装Python 3.8
2. 安装必要的依赖：pip install pillow tkinter
3. 直接运行Python脚本而不是exe文件

## 注意事项
- Windows 7已停止支持，建议升级到Windows 10或11
- Python 3.9+不再官方支持Windows 7
- 某些新版本的库可能不兼容Windows 7

## 联系支持
如果以上方案都无法解决问题，请提供：
1. Windows 7的具体版本（SP1等）
2. 完整的错误信息截图
3. 使用的Python版本和PyInstaller版本
'''
    
    with open('Windows7修复指南.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✓ 创建了 Windows7修复指南.md")

def main():
    print("=== Windows 7运行时修复工具 ===")
    print()
    
    # 创建各种修复脚本
    create_installer_script()
    create_download_script()
    create_manual_fix_guide()
    
    print()
    print("=== 创建完成 ===")
    print()
    print("已创建以下文件：")
    print("1. install_runtime_win7.bat - 运行时库安装脚本")
    print("2. download_runtime.bat - 下载运行时库脚本")
    print("3. Windows7修复指南.md - 详细修复指南")
    print()
    print("使用步骤：")
    print("1. 在有网络的电脑上运行 download_runtime.bat")
    print("2. 将下载的文件复制到Windows 7电脑")
    print("3. 在Windows 7上以管理员身份运行 install_runtime_win7.bat")
    print("4. 重启电脑后测试程序")

if __name__ == "__main__":
    main()
