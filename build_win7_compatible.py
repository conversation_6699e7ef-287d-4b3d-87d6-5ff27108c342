"""
Windows 7兼容性打包脚本
使用此脚本在Python 3.8环境中打包程序以确保Windows 7兼容性
"""

import subprocess
import sys
import os

def check_python_version():
    """检查Python版本是否适合Windows 7"""
    version = sys.version_info
    if version.major == 3 and version.minor <= 8:
        print(f"✓ Python {version.major}.{version.minor} 适合Windows 7兼容性")
        return True
    else:
        print(f"⚠ Python {version.major}.{version.minor} 可能不兼容Windows 7")
        print("建议使用Python 3.8或更早版本")
        return False

def install_dependencies():
    """安装必要的依赖"""
    dependencies = [
        "pyinstaller==5.13.2",  # 使用较旧但稳定的版本
        "pillow==9.5.0",        # 兼容版本
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✓ {dep} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ {dep} 安装失败: {e}")
            return False
    return True

def build_executable():
    """构建可执行文件"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['photo_resizer.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='照片处理器_Win7兼容版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    # Windows 7兼容性设置
    version_file=None,
    uac_admin=False,
    uac_uiaccess=False,
)
'''
    
    # 写入spec文件
    with open('photo_resizer_win7.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("开始构建可执行文件...")
    try:
        # 使用特定参数确保Windows 7兼容性
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "photo_resizer_win7.spec"
        ]
        subprocess.check_call(cmd)
        print("✓ 构建成功！")
        print("可执行文件位置: dist/照片处理器_Win7兼容版.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        return False

def main():
    print("=== Windows 7兼容性打包工具 ===")
    print()
    
    # 检查Python版本
    if not check_python_version():
        print("\n建议步骤:")
        print("1. 安装Python 3.8")
        print("2. 在Python 3.8环境中运行此脚本")
        return
    
    print()
    
    # 安装依赖
    if not install_dependencies():
        print("依赖安装失败，请检查网络连接")
        return
    
    print()
    
    # 构建可执行文件
    if build_executable():
        print("\n=== 构建完成 ===")
        print("请在Windows 7系统上测试生成的exe文件")
        print("如果仍有问题，请尝试方案2或3")
    else:
        print("构建失败，请检查错误信息")

if __name__ == "__main__":
    main()
