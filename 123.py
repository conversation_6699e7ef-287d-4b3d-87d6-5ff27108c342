import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg') 
import matplotlib.pyplot as plt
import matplotlib.style as style
import matplotlib.font_manager as fm


def set_chinese_font():

    print("  - 正在查找并设置可用的中文字体...")
    try:
        # 优先尝试的字体列表（覆盖Windows、macOS、Linux）
        font_preferences = [
            'Microsoft YaHei', 'SimHei', 'Heiti TC', 'PingFang SC', 
            'WenQuanYi Micro Hei', 'Noto Sans CJK SC'
        ]
        for font_name in font_preferences:
            if font_name in [f.name for f in fm.fontManager.ttflist]:
                plt.rcParams['font.sans-serif'] = [font_name]
                plt.rcParams['axes.unicode_minus'] = False
                print(f"  ✅ 成功找到并设置字体: {font_name}")
                return

        # 如果优先列表中的字体都找不到，则遍历所有字体
        print("  - 未找到常用字体，正在遍历系统所有字体...")
        for font in fm.fontManager.ttflist:
            # 'SimSun' 'NSimSun' 是宋体，也支持中文
            if 'SimSun' in font.name or 'NSimSun' in font.name:
                plt.rcParams['font.sans-serif'] = [font.name]
                plt.rcParams['axes.unicode_minus'] = False
                print(f"  ✅ 成功找到并设置备用字体: {font.name}")
                return
        
        # 如果还是找不到
        print("  ❌ 警告：系统中未找到任何可用的中文字体。图表中的中文可能会显示为方框。")
        print("     请在您的系统中安装任一中文字体（如'微软雅黑', '等线', '思源黑体'等）后重试。")

    except Exception as e:
        print(f"  ❌ 设置中文字体时发生错误: {e}")

class PatentedWeightedKMeans:
    """
    本发明方法的核心类实现。(代码与您提供的版本完全一致，此处不再重复)
    """
    def __init__(self, n_clusters: int, m: int = 3, k_gain: float = 10.0, delta_t_seconds: int = 120, max_iter: int = 100):
        self.k = n_clusters; self.m = m; self.k_gain = k_gain
        self.delta_t = pd.to_timedelta(delta_t_seconds, unit='s')
        self.max_iter = max_iter; self.epsilon = 1e-9
        self.cluster_centers_ = None; self.labels_ = None; self.weights_ = None
        self.alphas_ = None; self.betas_ = None; self.gammas_ = None
    def _calculate_alpha_p(self, data_points: np.ndarray) -> np.ndarray:
        n_points = data_points.shape[0]; alphas = np.zeros(n_points)
        print("  - 正在计算 alpha_p ...")
        for t in range(self.m, n_points):
            p_t = data_points[t]; history_window = data_points[t - self.m : t]
            distances = np.linalg.norm(p_t - history_window, axis=1)
            alphas[t] = np.min(distances) if distances.size > 0 else 0
        return alphas
    def _calculate_beta_p(self, ts_data: pd.DataFrame, op_logs_df: pd.DataFrame) -> np.ndarray:
        data_points = ts_data.values; n_points, n_dims = data_points.shape
        betas = np.zeros(n_points); log_timestamps = pd.to_datetime(op_logs_df['timestamp'])
        print("  - 正在计算 beta_p ...")
        for t in range(1, n_points - 1):
            m_abs = (data_points[t] - data_points[t-1])**2 + (data_points[t] - data_points[t+1])**2
            m_total = np.sum(m_abs)
            if m_total < self.epsilon: s_syn = 0
            else:
                c_rel = m_abs / m_total
                s_syn = 1 - np.sqrt(np.sum(c_rel * (c_rel - 1/n_dims)**2))
                s_syn = np.maximum(0, s_syn)
            current_timestamp = ts_data.index[t]
            s_mat = 1 if np.any(np.abs(log_timestamps - current_timestamp) <= self.delta_t) else 0
            betas[t] = (1 + s_mat) * s_syn
        return betas
    def _calculate_w_p(self, alphas: np.ndarray, betas: np.ndarray) -> np.ndarray:
        print("  - 正在计算 W_p...")
        max_alpha = np.max(alphas)
        alphas_normalized = alphas / max_alpha if max_alpha > self.epsilon else alphas
        beta_scale_factor = 100.0; betas_scaled = betas * beta_scale_factor
        self.gammas_ = alphas_normalized * (1 + betas_scaled)
        non_zero_gammas = self.gammas_[self.gammas_ > self.epsilon]
        m_gamma = np.median(non_zero_gammas) if len(non_zero_gammas) > 0 else self.epsilon
        relative_abnormality = (self.gammas_ / (m_gamma + self.epsilon)) - 1
        weights = 1 + (1 / np.pi) * np.arctan(self.k_gain * relative_abnormality) + 0.5
        return weights
    def fit(self, ts_data: pd.DataFrame, op_logs: pd.DataFrame):
        data_points = ts_data.values
        self.alphas_ = self._calculate_alpha_p(data_points)
        self.betas_ = self._calculate_beta_p(ts_data, op_logs)
        self.weights_ = self._calculate_w_p(self.alphas_, self.betas_)
        n_points = data_points.shape[0]; initial_center_indices = []
        initial_center_indices.append(np.argmax(self.weights_))
        for _ in range(1, self.k):
            min_dists_sq = np.full(n_points, np.inf)
            for idx in initial_center_indices:
                dists_sq = self.weights_ * np.sum((data_points - data_points[idx])**2, axis=1)
                min_dists_sq = np.minimum(min_dists_sq, dists_sq)
            probs = min_dists_sq / np.sum(min_dists_sq)
            next_center_idx = np.random.choice(n_points, p=probs)
            initial_center_indices.append(next_center_idx)
        self.cluster_centers_ = data_points[initial_center_indices]
        self.labels_ = np.zeros(n_points, dtype=int)
        print("  - 正在执行加权 K-means 迭代...")
        for i in range(self.max_iter):
            distances_to_centers = np.zeros((n_points, self.k))
            for j in range(self.k):
                distances_to_centers[:, j] = np.sqrt(
                    self.weights_.reshape(-1, 1) * np.sum((data_points - self.cluster_centers_[j])**2, axis=1, keepdims=True)
                ).flatten()
            new_labels = np.argmin(distances_to_centers, axis=1)
            if i > 0 and np.array_equal(self.labels_, new_labels):
                print(f"    算法在第 {i+1} 次迭代后收敛 (标签未变化)。"); break
            self.labels_ = new_labels
            new_centers_list = [data_points[self.labels_ == j].mean(axis=0) for j in range(self.k) if np.sum(self.labels_ == j) > 0]
            if len(new_centers_list) < self.k:
                print(f"    警告：在第 {i+1} 次迭代中出现空簇，聚类提前终止。"); break
            new_centers = np.array(new_centers_list)
            if np.allclose(self.cluster_centers_, new_centers):
                print(f"    算法在第 {i+1} 次迭代后收敛 (质心未变化)。"); break
            self.cluster_centers_ = new_centers
        print("聚类完成."); return self

def generate_realistic_agri_data_modified(timestamps):

    n_points = len(timestamps); hours = np.arange(n_points) / 3600.0
    temp_baseline = 22 + 8 * np.sin((hours - 8) * np.pi / 12); humidity_baseline = 65 - 20 * np.sin((hours - 8) * np.pi / 12)
    co2_day = 400 - 50 * np.sin(hours * np.pi / 24); co2_night = 450 + 50 * np.cos((hours - 12) * np.pi/12)
    co2_baseline = np.where((hours > 6) & (hours < 18), co2_day, co2_night)
    data = pd.DataFrame({'temperature': temp_baseline, 'humidity': humidity_baseline, 'co2': co2_baseline}, index=timestamps)
    data += np.random.normal(0, [0.1, 0.5, 2.0], size=(n_points, 3))
    op_logs_list = []
    start_idx = 10 * 3600; duration_s = 5 * 60; event_slice = slice(start_idx, start_idx + duration_s)
    data.loc[timestamps[event_slice], 'humidity'] = 98.0 + np.random.normal(0, 0.5, duration_s)
    data.loc[timestamps[event_slice], 'temperature'] -= 8.0
    op_logs_list.append({'timestamp': timestamps[start_idx], 'operation': 'Misting System Activated'})
    misting_event_indices = list(range(start_idx, start_idx + duration_s))
    start_idx = 14 * 3600; peak_duration_s = 10; decay_duration_s = 20 * 60; peak_slice = slice(start_idx, start_idx + peak_duration_s)
    data.loc[timestamps[peak_slice], 'co2'] = 1500 + np.random.normal(0, 20, peak_duration_s)
    decay_slice = slice(start_idx + peak_duration_s, start_idx + peak_duration_s + decay_duration_s)
    decay_len = len(data.iloc[decay_slice]); decay_factor = np.exp(-np.arange(decay_len) / (decay_duration_s / 5))
    decay_co2 = data['co2'].iloc[start_idx + peak_duration_s - 1] * decay_factor
    data.loc[timestamps[decay_slice], 'co2'] = np.maximum(data.loc[timestamps[decay_slice], 'co2'], decay_co2)
    op_logs_list.append({'timestamp': timestamps[start_idx], 'operation': 'CO2 Injection'})
    noise_idx = 20 * 3600; data.loc[timestamps[noise_idx], 'humidity'] += 15.0
    op_logs = pd.DataFrame(op_logs_list); 
    return data, op_logs, misting_event_indices, noise_idx


def visualize_final_separation(ts_data, results_df, misting_indices, co2_idx, noise_idx, filename="patented_method_final_visualization.png"):

    # 设置样式，确保字体设置生效
    style.use('ggplot')
    set_chinese_font() # 在绘图前调用我们的字体设置函数

    fig = plt.figure(figsize=(30, 25))
    gs = fig.add_gridspec(3, 3, hspace=0.5, wspace=0.3)
    fig.suptitle('一种用于智慧乡村的农业数据存储方法', fontsize=28, weight='bold')

    # --- 1. 全局数据概览 ---
    ax_full_data = fig.add_subplot(gs[0, :])
    for col in ts_data.columns:
        ax_full_data.plot(ts_data.index, ts_data[col], label=col, alpha=0.7)
    ax_full_data.set_title('1. 一日时序数据', fontsize=18, loc='left')
    ax_full_data.legend(title='传感器维度'); ax_full_data.set_ylabel('传感器读数')
    misting_start_dt, misting_end_dt = ts_data.index[misting_indices[0]-120], ts_data.index[misting_indices[-1]+120]
    co2_start_dt, co2_end_dt = ts_data.index[co2_idx-120], ts_data.index[co2_idx+1800]
    noise_start_dt, noise_end_dt = ts_data.index[noise_idx-120], ts_data.index[noise_idx+120]
    ax_full_data.axvspan(misting_start_dt, misting_end_dt, color='green', alpha=0.15, label='弥雾事件区')
    ax_full_data.axvspan(co2_start_dt, co2_end_dt, color='blue', alpha=0.15, label='CO2事件区')
    ax_full_data.axvspan(noise_start_dt, noise_end_dt, color='red', alpha=0.15, label='随机噪声区')
    ax_full_data.legend()

    # --- 2. 核心因子展示 ---
    ax_alpha = fig.add_subplot(gs[1, :2])
    ax_beta = fig.add_subplot(gs[1, 2])
    ax_alpha.plot(results_df.index, results_df['alpha_p'], 'b.', markersize=0.5); ax_alpha.set_title(r'2a. 初始突变强度 ($\alpha_p$)', fontsize=18, loc='left')
    ax_alpha.set_ylabel(r'α_p 值'); ax_alpha.set_yscale('log'); ax_alpha.set_xlabel('时间')
    ax_beta.plot(results_df.index, results_df['beta_p'], 'r.', markersize=0.5); ax_beta.set_title(r'2b. 事件可信度 ($\beta_p$)', fontsize=18, loc='left')
    ax_beta.set_ylabel(r'β_p 值'); ax_beta.set_xlabel('时间')

    # --- 3. 局部聚类结果的对比验证 ---
    cmap = plt.get_cmap('tab10')
    
    # 图3a: 弥雾事件 vs 常规数据
    ax_event1 = fig.add_subplot(gs[2, 0])
    local_df1 = results_df.loc[misting_start_dt:misting_end_dt]
    background1 = local_df1[~local_df1.index.isin(ts_data.index[misting_indices])]
    ax_event1.scatter(background1.index, ts_data.loc[background1.index]['humidity'], c='lightgray', s=15, label='常规数据')
    event1 = local_df1[local_df1.index.isin(ts_data.index[misting_indices])]
    misting_label = int(event1['label'].iloc[0])
    ax_event1.scatter(event1.index, ts_data.loc[event1.index]['humidity'], c=[cmap(misting_label)], s=60, label=f'事件数据 (簇 {misting_label})', edgecolors='black')
    ax_event1.set_title(f'3a. 弥雾事件被分离为独立簇', fontsize=16, loc='left', weight='bold')
    ax_event1.set_ylabel('湿度'); ax_event1.set_xlabel('时间'); ax_event1.legend()
    
    # 图3b: CO2事件 vs 常规数据
    ax_event2 = fig.add_subplot(gs[2, 1])
    local_df2 = results_df.loc[co2_start_dt:co2_end_dt]
    co2_event_indices = range(co2_idx, co2_idx + 10*60)
    background2 = local_df2[~local_df2.index.isin(ts_data.index[co2_event_indices])]
    ax_event2.scatter(background2.index, ts_data.loc[background2.index]['co2'], c='lightgray', s=15, label='常规数据')
    event2 = local_df2[local_df2.index.isin(ts_data.index[co2_event_indices])]
    co2_label = int(event2['label'].iloc[0])
    ax_event2.scatter(event2.index, ts_data.loc[event2.index]['co2'], c=[cmap(co2_label)], s=60, label=f'事件数据 (簇 {co2_label})', edgecolors='black')
    ax_event2.set_title(f'3b. CO2事件被分离为独立簇', fontsize=16, loc='left', weight='bold')
    ax_event2.set_ylabel('CO2浓度'); ax_event2.set_xlabel('时间'); ax_event2.legend()

    # 图3c: 随机噪声 vs 常规数据
    ax_noise = fig.add_subplot(gs[2, 2])
    local_df3 = results_df.loc[noise_start_dt:noise_end_dt]
    noise_label = int(local_df3.loc[ts_data.index[noise_idx]]['label'])
    ax_noise.scatter(local_df3.index, ts_data.loc[local_df3.index]['humidity'], c=local_df3['label'], cmap=cmap, s=20)
    ax_noise.scatter(ts_data.index[noise_idx], ts_data.loc[ts_data.index[noise_idx]]['humidity'], 
                     facecolors='none', edgecolors='red', s=200, linewidth=2, label='噪声点')
    ax_noise.set_title(f'3c. 随机噪声被常规簇 {noise_label} 吸收', fontsize=16, loc='left', weight='bold')
    ax_noise.set_ylabel('湿度'); ax_noise.set_xlabel('时间'); ax_noise.legend()
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"\n最终演示结果图表已保存为文件: {filename}")


if __name__ == '__main__':
    N_POINTS = 24 * 60 * 60
    timestamps = pd.to_datetime(pd.date_range(start='2023-01-01 00:00:00', periods=N_POINTS, freq='1S'))
    data, op_logs, misting_event_indices, noise_idx = generate_realistic_agri_data_modified(timestamps)
    co2_event_idx = 14 * 3600

    print("\n--- 2. 执行本发明方法 ---")
    k_clusters = 4
    
    patented_wkm = PatentedWeightedKMeans(
        n_clusters=k_clusters, 
        m=10, k_gain=50.0, delta_t_seconds=300, max_iter=100
    )
    patented_wkm.fit(data, op_logs)
    
    # --- 3. 结果分析 ---
    print("\n--- 3. 结果展示---")
    results_df = data.copy()
    results_df['alpha_p'] = patented_wkm.alphas_
    results_df['beta_p'] = patented_wkm.betas_
    results_df['W_p'] = patented_wkm.weights_
    results_df['label'] = patented_wkm.labels_
    
    misting_label = results_df.iloc[misting_event_indices[0]]['label']
    co2_label = results_df.iloc[co2_event_idx]['label']
    noise_label = results_df.iloc[noise_idx]['label']
    day_label = results_df.iloc[12 * 3600]['label']
    night_label = results_df.iloc[2 * 3600]['label']



    # --- 4. 生成可视化证明文件 ---
    visualize_final_separation(data, results_df, misting_event_indices, co2_event_idx, noise_idx)