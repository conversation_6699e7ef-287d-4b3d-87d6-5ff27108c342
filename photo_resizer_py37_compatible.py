import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageOps
from threading import Thread

# --- 核心处理逻辑 (Python 3.7兼容版) ---

DPI = 300
INCH_TO_CM = 2.54
LONG_SIDE_CM = 12.7
SHORT_SIDE_CM = 8.9
LONG_SIDE_PX = int((LONG_SIDE_CM / INCH_TO_CM) * DPI)
SHORT_SIDE_PX = int((SHORT_SIDE_CM / INCH_TO_CM) * DPI)
PORTRAIT_SIZE = (SHORT_SIDE_PX, LONG_SIDE_PX)
LANDSCAPE_SIZE = (LONG_SIDE_PX, SHORT_SIDE_PX)

def process_image(input_path, output_path, orientation_mode):
    """
    处理单个图片文件，根据指定的方向模式进行正确的旋转和拉伸。
    Python 3.7兼容版本
    """
    try:
        with Image.open(input_path) as img:
            # 1. 根据EXIF信息自动旋转图片，确保其视觉方向正确
            # Python 3.7兼容性检查
            try:
                img = ImageOps.exif_transpose(img)
            except AttributeError:
                # 如果没有exif_transpose方法，使用手动处理
                try:
                    exif = img._getexif()
                    if exif is not None:
                        orientation = exif.get(274)  # Orientation tag
                        if orientation == 3:
                            img = img.rotate(180, expand=True)
                        elif orientation == 6:
                            img = img.rotate(270, expand=True)
                        elif orientation == 8:
                            img = img.rotate(90, expand=True)
                except:
                    pass  # 如果无法处理EXIF，继续处理
            
            if img.mode == 'RGBA' or 'A' in img.getbands():
                img = img.convert('RGB')
            
            # 获取图片在视觉上的真实宽高
            width, height = img.size
            is_source_portrait = height >= width

            # --- 这是本次修正的核心逻辑 ---
            
            image_to_process = img
            
            # 2. 判断是否需要旋转内容
            # 情况一：要求统一为竖版，但当前是横版照片
            if orientation_mode == 'portrait' and not is_source_portrait:
                # 先旋转90度，使其内容变为竖向
                image_to_process = img.rotate(90, expand=True)
            
            # 情况二：要求统一为横版，但当前是竖版照片
            elif orientation_mode == 'landscape' and is_source_portrait:
                # 先旋转90度，使其内容变为横向
                image_to_process = img.rotate(90, expand=True)

            # 3. 确定最终的目标尺寸
            if orientation_mode == 'portrait':
                target_size = PORTRAIT_SIZE
            elif orientation_mode == 'landscape':
                target_size = LANDSCAPE_SIZE
            else: # 'auto' 模式
                target_size = PORTRAIT_SIZE if is_source_portrait else LANDSCAPE_SIZE
                
            # 4. 对已经旋转好（或无需旋转）的图像进行拉伸
            # Python 3.7兼容的resize方法
            try:
                # 尝试新版本语法
                final_img = image_to_process.resize(target_size, resample=Image.Resampling.LANCZOS)
            except AttributeError:
                # 使用旧版本语法
                final_img = image_to_process.resize(target_size, resample=Image.LANCZOS)
            
            # 5. 保存
            final_img.save(output_path, dpi=(DPI, DPI), quality=95)
        return True
    except Exception as e:
        print("处理文件 {} 时出错: {}".format(input_path, str(e)))  # Python 3.7兼容的字符串格式化
        return False

# --- 图形用户界面 (GUI) ---

class ImageProcessorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("改8.9×12.7cm照片批量处理器 (Python3.7兼容版)")
        self.root.geometry("600x600")

        self.input_files = []
        self.output_dir = ""

        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 1. 输入选择区域
        input_frame = ttk.LabelFrame(main_frame, text="第一步：选择图片文件或文件夹", padding="10")
        input_frame.pack(fill=tk.X, pady=5)

        self.files_listbox = tk.Listbox(input_frame, height=8)
        self.files_listbox.pack(side=tk.TOP, fill=tk.BOTH, expand=True)
        scrollbar = ttk.Scrollbar(input_frame, orient=tk.VERTICAL, command=self.files_listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, before=self.files_listbox)
        self.files_listbox.config(yscrollcommand=scrollbar.set)
        
        button_container = ttk.Frame(input_frame)
        button_container.pack(fill=tk.X, pady=(10, 0))
        btn_select_files = ttk.Button(button_container, text="选择文件", command=self.select_files)
        btn_select_files.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        btn_select_folder = ttk.Button(button_container, text="选择文件夹", command=self.select_folder)
        btn_select_folder.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.include_subfolders_var = tk.BooleanVar(value=True)
        check_subfolders = ttk.Checkbutton(button_container, text="包含子文件夹", variable=self.include_subfolders_var)
        check_subfolders.pack(side=tk.RIGHT, padx=5)

        # 2. 输出方向设置区域
        orientation_frame = ttk.LabelFrame(main_frame, text="第二步：选择输出方向", padding="10")
        orientation_frame.pack(fill=tk.X, pady=10)
        
        self.orientation_mode_var = tk.StringVar(value="auto")
        
        radio_auto = ttk.Radiobutton(orientation_frame, text="自动识别", variable=self.orientation_mode_var, value="auto")
        radio_auto.pack(side=tk.LEFT, expand=True, padx=10)
        
        radio_portrait = ttk.Radiobutton(orientation_frame, text="统一为竖版 (8.9x12.7cm)", variable=self.orientation_mode_var, value="portrait")
        radio_portrait.pack(side=tk.LEFT, expand=True, padx=10)
        
        radio_landscape = ttk.Radiobutton(orientation_frame, text="统一为横版 (12.7x8.9cm)", variable=self.orientation_mode_var, value="landscape")
        radio_landscape.pack(side=tk.LEFT, expand=True, padx=10)

        # 3. 输出文件夹选择区域
        output_frame = ttk.LabelFrame(main_frame, text="第三步：选择保存位置", padding="10")
        output_frame.pack(fill=tk.X, pady=5)

        self.output_path_var = tk.StringVar()
        output_entry = ttk.Entry(output_frame, textvariable=self.output_path_var, state="readonly")
        output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        btn_select_output = ttk.Button(output_frame, text="浏览...", command=self.select_output_dir)
        btn_select_output.pack(side=tk.RIGHT)

        # 4. 处理与进度条区域
        process_frame = ttk.Frame(main_frame, padding="10")
        process_frame.pack(fill=tk.X, pady=10)
        self.start_button = ttk.Button(process_frame, text="开始处理", command=self.start_processing_thread)
        self.start_button.pack(fill=tk.X, ipady=5)
        self.progress_bar = ttk.Progressbar(main_frame, orient="horizontal", mode="determinate")
        self.progress_bar.pack(fill=tk.X, pady=5)
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪...")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, anchor=tk.W)
        status_label.pack(fill=tk.X)

    def update_file_list(self, files):
        if files:
            self.input_files = files
            self.files_listbox.delete(0, tk.END)
            for file in self.input_files:
                self.files_listbox.insert(tk.END, os.path.basename(file))
            self.status_var.set("已加载 {} 个文件。".format(len(self.input_files)))
        else:
            self.input_files = []
            self.files_listbox.delete(0, tk.END)
            self.status_var.set("未找到任何图片文件。")

    def select_files(self):
        filetypes = [("图片文件", "*.jpg *.jpeg *.png *.bmp *.tif *.tiff"), ("所有文件", "*.*")]
        files = filedialog.askopenfilenames(title="选择一个或多个图片文件", filetypes=filetypes)
        self.update_file_list(list(files))

    def select_folder(self):
        directory = filedialog.askdirectory(title="选择包含图片的文件夹")
        if not directory: return
        found_files = []
        supported_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')
        if self.include_subfolders_var.get():
            for root, _, files in os.walk(directory):
                for name in files:
                    if name.lower().endswith(supported_extensions):
                        found_files.append(os.path.join(root, name))
        else:
            for name in os.listdir(directory):
                path = os.path.join(directory, name)
                if os.path.isfile(path) and name.lower().endswith(supported_extensions):
                    found_files.append(path)
        self.update_file_list(found_files)
        if not found_files:
            messagebox.showinfo("提示", "在所选文件夹中未找到支持的图片文件。")

    def select_output_dir(self):
        directory = filedialog.askdirectory(title="选择保存处理后图片的文件夹")
        if directory:
            self.output_dir = directory
            self.output_path_var.set(directory)

    def start_processing_thread(self):
        if not self.input_files:
            messagebox.showwarning("警告", "请先选择要处理的图片文件或文件夹！")
            return
        if not self.output_dir:
            messagebox.showwarning("警告", "请先选择保存位置！")
            return
        processing_thread = Thread(target=self.process_files)
        processing_thread.start()

    def process_files(self):
        self.start_button.config(state="disabled")
        self.progress_bar["value"] = 0
        self.progress_bar["maximum"] = len(self.input_files)
        selected_mode = self.orientation_mode_var.get()
        total_files = len(self.input_files)
        success_count = 0
        for i, file_path in enumerate(self.input_files):
            self.status_var.set("正在处理: {} ({}/{})".format(os.path.basename(file_path), i+1, total_files))
            filename = "processed_" + os.path.basename(file_path)
            output_path = os.path.join(self.output_dir, filename)
            if process_image(file_path, output_path, selected_mode):
                success_count += 1
            self.progress_bar["value"] = i + 1
            self.root.update_idletasks()
        self.status_var.set("处理完成！成功 {} 个，失败 {} 个。".format(success_count, total_files - success_count))
        messagebox.showinfo("完成", "所有图片处理完毕！\n\n成功: {}\n失败: {}\n\n文件已保存至:\n{}".format(success_count, total_files - success_count, self.output_dir))
        self.start_button.config(state="normal")

if __name__ == "__main__":
    root = tk.Tk()
    app = ImageProcessorApp(root)
    root.mainloop()
