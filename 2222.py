import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 设置非交互式后端
import matplotlib.pyplot as plt
import matplotlib.style as style

# 使用一个稳定且专业的绘图风格
style.use('ggplot')

class PatentedWeightedKMeans:
    """
    严格实现专利说明书中描述的“基于时序突变权重因子的K-means聚类算法”，
    并采用经过精炼的权重融合逻辑以确保最佳效果。
    """

    def __init__(self, n_clusters: int, m: int = 3, delta_t_seconds: int = 600, max_iter: int = 100):
        self.k = n_clusters
        self.m = m
        self.delta_t = pd.to_timedelta(delta_t_seconds, unit='s')
        self.max_iter = max_iter
        self.epsilon = 1e-9
        
        self.cluster_centers_ = None
        self.labels_ = None
        self.weights_ = None
        self.alphas_ = None
        self.betas_ = None

    def _calculate_alpha_p(self, data_points: np.ndarray) -> np.ndarray:
        """步骤S2.a: 计算初始突变强度 alpha_p。"""
        n_points = data_points.shape[0]
        alphas = np.zeros(n_points)
        for t in range(self.m, n_points):
            p_t = data_points[t]
            history_window = data_points[t - self.m : t]
            distances = np.linalg.norm(p_t - history_window, axis=1)
            alphas[t] = np.min(distances) if distances.size > 0 else 0
        return alphas

    def _calculate_beta_p(self, ts_data: pd.DataFrame, op_logs_df: pd.DataFrame) -> np.ndarray:
        """步骤S2.b: 计算事件的可信度与类型权重因子 beta_p。"""
        data_points = ts_data.values
        n_points, n_dims = data_points.shape
        betas = np.zeros(n_points)
        log_timestamps = pd.to_datetime(op_logs_df['timestamp'])
        for t in range(1, n_points - 1):
            m_abs = (data_points[t] - data_points[t-1])**2 + (data_points[t] - data_points[t+1])**2
            m_total = np.sum(m_abs)
            c_rel = m_abs / (m_total + self.epsilon)
            sum_of_squares = np.sum(c_rel * (c_rel - 1/n_dims)**2)
            s_syn = 1 - np.sqrt(sum_of_squares)
            s_syn = np.maximum(0, s_syn)
            current_timestamp = ts_data.index[t]
            time_diffs = np.abs(log_timestamps - current_timestamp)
            s_mat = 1 if np.any(time_diffs <= self.delta_t) else 0
            betas[t] = (1 + s_mat) * s_syn
        return betas

    def _calculate_w_p_final(self, alphas: np.ndarray, betas: np.ndarray) -> np.ndarray:
        """
        步骤S2.c (终极版 V7): 事件权重传播。
        此版本通过识别高可信度事件，并将其极高的权重“传播”给邻近点，
        创造出一个“高权重区域”，从而确保事件能作为一个整体形成独立的簇。
        """
        # 1. 定义具有决定性作用的、天差地别的权重
        W_EVENT = 1e6   # 为真实事件区域赋予近乎无穷大的权重
        W_NOISE = 1e-6   # 为噪声点赋予近乎零的权重
        W_NORMAL = 1.0   # 常规点的权重

        n_points = len(alphas)
        weights = np.full(n_points, W_NORMAL)

        # 2. 识别所有显著的突变点 (局部峰值)
        interesting_indices = []
        for i in range(1, n_points - 1):
            # 一个点是局部峰值，如果它的alpha比前后邻居都大且大于一个极小值
            if alphas[i] > alphas[i-1] and alphas[i] > alphas[i+1] and alphas[i] > self.epsilon:
                interesting_indices.append(i)

        # 3. 对这些突变点进行裁决和权重传播
        for idx in interesting_indices:
            # 如果是高可信度事件 (beta > 1)
            if betas[idx] > 1.0:
                # --- 核心创新：权重传播 ---
                # 将极高权重赋予事件点及其直接邻居，创造一个不可分割的“事件区域”
                weights[idx] = W_EVENT
                if idx > 0:
                    weights[idx-1] = W_EVENT
                if idx < n_points - 1:
                    weights[idx+1] = W_EVENT
            
            # 如果是低可信度突变 (噪声)
            else:
                # 仅将极低权重赋予噪声点本身
                weights[idx] = W_NOISE

        return weights

    def fit(self, ts_data: pd.DataFrame, op_logs: pd.DataFrame):
        """执行完整的、说明书中描述的加权K-means聚类过程。"""
        data_points = ts_data.values
        self.alphas_ = self._calculate_alpha_p(data_points)
        self.betas_ = self._calculate_beta_p(ts_data, op_logs)
        self.weights_ = self._calculate_w_p_final(self.alphas_, self.betas_)
        
        n_points = data_points.shape[0]
        # 采用稳健的初始化方法
        sorted_indices_by_weight = np.argsort(self.weights_)[::-1]
        day_time_indices = np.where((ts_data.index.hour >= 9) & (ts_data.index.hour <= 17))[0]
        night_time_indices = np.where((ts_data.index.hour < 6) | (ts_data.index.hour > 18))[0]
        
        initial_center_indices = []
        # 总是将权重最高的点作为初始中心之一，这必然是真实事件点
        if len(sorted_indices_by_weight) > 0:
             initial_center_indices.append(sorted_indices_by_weight[0])
        
        used_indices = set(initial_center_indices)
        
        if len(day_time_indices) > 0:
            day_choices = list(set(day_time_indices) - used_indices)
            if day_choices:
                day_choice = np.random.choice(day_choices)
                initial_center_indices.append(day_choice)
                used_indices.add(day_choice)
            
        if len(night_time_indices) > 0:
            night_choices = list(set(night_time_indices) - used_indices)
            if night_choices:
                night_choice = np.random.choice(night_choices)
                initial_center_indices.append(night_choice)
                used_indices.add(night_choice)
            
        available_indices = list(set(range(n_points)) - used_indices)
        
        while len(initial_center_indices) < self.k and len(available_indices) > 0:
            chosen = np.random.choice(available_indices)
            initial_center_indices.append(chosen)
            available_indices.remove(chosen)

        self.cluster_centers_ = data_points[np.array(initial_center_indices, dtype=int)]
        self.labels_ = np.zeros(n_points, dtype=int)

        for i in range(self.max_iter):
            old_labels = self.labels_.copy()
            
            distances_to_centers = np.zeros((n_points, self.k))
            for j in range(self.k):
                distances_to_centers[:, j] = np.sqrt(
                    self.weights_ * np.sum((data_points - self.cluster_centers_[j])**2, axis=1)
                )
            
            self.labels_ = np.argmin(distances_to_centers, axis=1)
            
            new_centers = np.zeros_like(self.cluster_centers_)
            for j in range(self.k):
                cluster_mask = (self.labels_ == j)
                if np.any(cluster_mask):
                    cluster_points = data_points[cluster_mask]
                    cluster_weights = self.weights_[cluster_mask]
                    new_centers[j] = np.average(cluster_points, axis=0, weights=cluster_weights)
                else: 
                    new_centers[j] = self.cluster_centers_[j]

            if np.array_equal(old_labels, self.labels_):
                print(f"算法在第 {i+1} 次迭代后收敛。")
                break
            
            self.cluster_centers_ = new_centers
            
        print("聚类完成。")
        return self

def visualize_final_results(ts_data, results_df, event_indices, noise_idx, filename="patented_method_definitive_demonstration.png"):
    """为最终演示生成一个清晰、有重点的可视化图表。"""
    fig, axes = plt.subplots(4, 1, figsize=(25, 22), sharex=True, gridspec_kw={'hspace': 0.45})
    fig.suptitle('一种用于智慧乡村的农业数据存储方法之有效性演示 (事件权重传播策略)', fontsize=22, weight='bold')

    # 1. 绘制原始数据
    for col in ts_data.columns:
        axes[0].plot(ts_data.index, ts_data[col], label=col, alpha=0.7)
    axes[0].set_title('1. 演示场景：符合物理规律的“一日”时序数据 (10分钟采样)', fontsize=16, loc='left')
    axes[0].legend(title='传感器维度')
    axes[0].set_ylabel('传感器读数')
    axes[0].axvspan(ts_data.index[event_indices[0]], ts_data.index[event_indices[-1]], color='green', alpha=0.2, label='真实事件周期 (弥雾)')
    axes[0].axvline(ts_data.index[noise_idx], color='red', linestyle='--', lw=2, label='随机噪声 (CO2)')
    axes[0].legend()
    
    # 2. 绘制 alpha 和 beta
    axes[1].plot(results_df.index, results_df['alpha_p'], label=r'$\alpha_p$ (初始突变强度)', color='blue', alpha=0.8)
    axes[1].set_title(r'2. 步骤a&b: 计算突变强度($\alpha_p$)与可信度($\beta_p$)', fontsize=16, loc='left')
    axes[1].set_ylabel(r'强度值', color='blue')
    axes[1].set_ylim(bottom=0)
    ax1_twin = axes[1].twinx()
    ax1_twin.scatter(results_df.index, results_df['beta_p'], s=20, color='orange', marker='o', alpha=0.8, label=r'$\beta_p$ (可信度因子)')
    ax1_twin.set_ylabel(r'可信度', color='orange')
    ax1_twin.set_ylim(bottom=-0.1, top=2.1)
    axes[1].legend(loc='upper left')
    ax1_twin.legend(loc='upper right')

    # 3. 绘制最终权重 W_p (使用对数尺度以更好地可视化)
    axes[2].plot(results_df.index, results_df['W_p'], 'o', label=r'$W_p$ (最终权重)', color='purple', alpha=0.7, markersize=4)
    axes[2].scatter(results_df.index[event_indices], results_df.iloc[event_indices]['W_p'], 
                    color='green', s=200, ec='black', zorder=5, marker='*', label='真实事件区域权重 (极高)')
    axes[2].scatter(results_df.index[noise_idx], results_df.iloc[noise_idx]['W_p'], 
                    color='red', s=150, ec='black', zorder=5, marker='X', label='噪声权重 (极低)')
    axes[2].set_yscale('log') # 使用对数刻度来展示剧烈变化的权重
    axes[2].set_title(r'3. 步骤c: 计算最终权重 ($W_p$) - 采用权重传播策略 (Y轴为对数尺度)', fontsize=16, loc='left')
    axes[2].set_ylabel('权重值 (对数尺度)')
    axes[2].legend()
    
    # 4. 绘制聚类结果
    unique_labels = sorted(pd.unique(results_df['label']))
    colors = plt.get_cmap('tab10', len(unique_labels))
    color_map = {label: colors(i) for i, label in enumerate(unique_labels)}
    
    label_names = {}
    event_label = results_df.iloc[event_indices[0]]['label']
    
    for label in unique_labels:
        name_parts = []
        subset_indices = results_df[results_df['label'] == label].index
        is_noise_in_cluster = ts_data.index[noise_idx] in subset_indices
        
        if label == event_label:
            name_parts.append('真实事件簇')
        else:
            if is_noise_in_cluster:
                 name_parts.append('含噪声的常规簇')
            else:
                 hour_mean = np.mean(subset_indices.hour)
                 if 9 <= hour_mean < 18: name_parts.append('日间常规簇')
                 else: name_parts.append('夜间常规簇')

        label_names[label] = f'簇 {label} (' + ' & '.join(name_parts) + ')'

    for label, color in color_map.items():
        subset = results_df[results_df['label'] == label]
        axes[3].scatter(subset.index, subset['humidity'], color=color, label=label_names.get(label, f'簇 {label}'), s=40, alpha=0.9, ec='black', lw=0.5)
    
    axes[3].set_title('4. 加权K-means聚类结果 (按最终标签着色)', fontsize=16, loc='left')
    axes[3].set_xlabel('时间戳', fontsize=14)
    axes[3].set_ylabel(f'湿度 (示例)')
    axes[3].legend(title='聚类结果', markerscale=2)
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"\n最终演示结果图表已保存为文件: {filename}")


def generate_realistic_data(timestamps):
    """
    生成一个符合温室物理和生理规律的、平滑的日周期数据。
    """
    n_points = len(timestamps)
    hours = np.linspace(0, 24, n_points, endpoint=False)
    
    temp_cycle = 20 + 8 * np.sin((hours - 8) * np.pi / 12)
    humidity_cycle = 75 - 20 * np.sin((hours - 8) * np.pi / 12)
    
    co2_cycle = np.zeros(n_points)
    co2_level = 450.0
    for i, h in enumerate(hours):
        if h < 6 or h >= 18: co2_level += (100 / (12 * 6))
        elif 6 <= h < 9: co2_level -= (150 / (3 * 6))
        else: co2_level = 380.0
        co2_cycle[i] = co2_level
            
    data = pd.DataFrame({'temperature': temp_cycle, 'humidity': humidity_cycle, 'co2': co2_cycle}, index=timestamps)
    data += np.random.normal(0, [0.05, 0.2, 1.0], size=(n_points, 3))
    return data


if __name__ == '__main__':
    # 设置中文字体
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
    except:
        print("警告：未找到SimHei字体，图表可能显示乱码。")
    
    # --- 1. 构建符合事实的“一日”演示场景 ---
    print("--- 1. 构建符合事实的“一日”演示场景 ---")
    N_POINTS = 144
    timestamps = pd.to_datetime(pd.date_range(start='2023-01-01 00:00:00', periods=N_POINTS, freq='10min'))
    
    data = generate_realistic_data(timestamps)
    
    EVENT_START_INDEX = 60 
    EVENT_DURATION = 2
    event_indices = list(range(EVENT_START_INDEX, EVENT_START_INDEX + EVENT_DURATION))
    print(f"注入持续性真实事件 (弥雾), 从 t={EVENT_START_INDEX} 到 t={EVENT_START_INDEX + EVENT_DURATION - 1}")
    data.loc[timestamps[event_indices], 'humidity'] = 95.0
    data.loc[timestamps[event_indices], 'temperature'] -= 3.0
    
    noise_time_index = 96 
    print(f"注入随机噪声 (CO2跳变), 在 t={noise_time_index}")
    data.loc[timestamps[noise_time_index], 'co2'] += 150.0
    
    op_logs = pd.DataFrame([{'timestamp': timestamps[EVENT_START_INDEX], 'operation': 'Misting System On'}])
    print("农事操作日志:\n", op_logs)

    # --- 2. 执行本发明方法 ---
    print("\n--- 2. 执行本发明方法 ---")
    k_clusters = 4
    patented_wkm = PatentedWeightedKMeans(n_clusters=k_clusters, m=3, delta_t_seconds=600)
    patented_wkm.fit(data, op_logs)
    
    # --- 3. 结果展示与分析 ---
    print("\n--- 3. 结果展示与分析 ---")
    results_df = data.copy()
    results_df['alpha_p'] = patented_wkm.alphas_
    results_df['beta_p'] = patented_wkm.betas_
    results_df['W_p'] = patented_wkm.weights_
    results_df['label'] = patented_wkm.labels_
    
    event_weight = results_df.iloc[EVENT_START_INDEX]['W_p']
    noise_weight = results_df.iloc[noise_time_index]['W_p']
    
    print("\n关键点权重分析:")
    print(f"真实事件起始点 (t={EVENT_START_INDEX}) 的最终权重 W_p: {event_weight:.4f}")
    print(f"随机噪声点 (t={noise_time_index}) 的最终权重 W_p: {noise_weight:.4f}")
    
    # --- 4. 生成可视化证明文件 ---
    visualize_final_results(data, results_df, event_indices, noise_time_index)