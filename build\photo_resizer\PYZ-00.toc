('G:\\project\\cesh<PERSON>hi\\智慧乡村\\build\\photo_resizer\\PYZ-00.pyz',
 [('typing',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\typing.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\contextlib.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\tokenize.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\textwrap.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\gettext.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\struct.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\token.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\_py_abc.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\doctest.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('ntpath',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\ntpath.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\string.py',
   'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\genericpath.py',
   'PYMODULE'),
  ('stat',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\stat.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\signal.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\_strptime.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\datetime.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\calendar.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\fnmatch.py',
   'PYMODULE'),
  ('posixpath',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\posixpath.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\pprint.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\pickle.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\pdb.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\getopt.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\webbrowser.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\selectors.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\shutil.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\py_compile.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\bz2.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\generator.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\random.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\bisect.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\hashlib.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\base64.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\quopri.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\optparse.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\http\\server.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\http\\client.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\ssl.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\tty.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\subprocess.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\tempfile.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\sysconfig.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\plistlib.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\netrc.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\pkgutil.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\runpy.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\shlex.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\glob.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\codeop.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\opcode.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\cmd.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\inspect.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\ast.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\difflib.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\__future__.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\_threading_local.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('os',
   'C:\\Users\\<USER>\\miniconda3\\envs\\medicine_py37\\lib\\os.py',
   'PYMODULE')])
